import type { Prisma, Site } from "@/generated/prisma";

export interface DTO {
  id: Site["id"];
  name: Site["name"];
  subdomain: Site["subdomain"];
  createdAt: Site["createdAt"];
  updatedAt: Site["createdAt"];
}

export interface ISitesQueries {
  getAllUserSites: () => Promise<DTO[]>;
  getSite: ({ id }: { id: string }) => Promise<DTO | null>;
  getSiteBySubdomain: ({
    subdomain,
  }: {
    subdomain: string;
  }) => Promise<DTO | null>;
}

export interface ISitesMutations {
  create: ({ data }: { data: Prisma.SiteCreateInput }) => Promise<DTO | null>;
  delete: ({ id }: { id: string }) => Promise<boolean>;
  update: ({
    id,
    data,
  }: {
    id: string;
    data: Prisma.SiteUpdateInput;
  }) => Promise<DTO | null>;
  upsert: ({
    id,
    create,
    update,
  }: {
    id: string;
    create: Prisma.SiteCreateInput;
    update: Prisma.SiteUpdateInput;
  }) => Promise<DTO | null>;
}
