import "server-only";

import { headers } from "next/headers";
import type { Site } from "@/generated/prisma";
import { auth } from "@/lib/auth";
import { db } from "@/lib/db";
import type { DTO, ISitesMutations, ISitesQueries } from "./types";

const toDTO = (site: Site): DTO => {
  return {
    id: site.id,
    name: site.name,
    subdomain: site.subdomain,
    createdAt: site.createdAt,
    updatedAt: site.updatedAt,
  };
};

const queries: ISitesQueries = {
  async getAllUserSites() {
    const session = await auth.api.getSession({ headers: await headers() });

    if (!session?.user) {
      return [];
    }

    const sites = await db.site.findMany({
      where: { userId: session.user.id },
    });

    return sites.map(toDTO);
  },
  async getSite({ id }) {
    const session = await auth.api.getSession({ headers: await headers() });

    if (!session?.user) {
      return null;
    }

    const site = await db.site.findUnique({
      where: { id, userId: session.user.id },
    });

    return site ? toDTO(site) : null;
  },
  async getSiteBySubdomain({ subdomain }) {
    const site = await db.site.findUnique({
      where: { subdomain },
    });

    return site ? toDTO(site) : null;
  },
};

const mutations: ISitesMutations = {
  async create({ data }) {
    const session = await auth.api.getSession({ headers: await headers() });

    if (!session?.user) {
      return null;
    }

    const site = await db.site.create({ data });

    return toDTO(site);
  },
  async delete({ id }) {
    const session = await auth.api.getSession({ headers: await headers() });

    if (!session?.user) {
      return false;
    }

    await db.site.delete({
      where: { id, userId: session.user.id },
    });

    return true;
  },
  async update({ id, data }) {
    const session = await auth.api.getSession({ headers: await headers() });

    if (!session?.user) {
      return null;
    }

    const site = await db.site.update({
      where: { id, userId: session.user.id },
      data,
    });

    return toDTO(site);
  },
  async upsert({ id, create, update }) {
    const session = await auth.api.getSession({ headers: await headers() });

    if (!session?.user) {
      return null;
    }

    const site = await db.site.upsert({
      where: { id, userId: session.user.id },
      update,
      create,
    });

    return toDTO(site);
  },
};

export const SiteService = { queries, mutations };
